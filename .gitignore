# Ignore node modules and package lock file
node_modules
package-lock.json

# Ignore task files
.roo/**

# Added by <PERSON> Task Master
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store


# Test workspace
test-workspace
.vscode-test
out

# Build output
dist/

.ipsa/
*.vsix
