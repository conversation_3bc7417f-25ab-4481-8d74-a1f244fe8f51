:root {
  --container-padding: 20px;
  --input-padding-vertical: 6px;
  --input-padding-horizontal: 4px;
  --input-margin-vertical: 4px;
  --input-margin-horizontal: 0;
}

body {
  padding: 0;
  color: var(--vscode-foreground);
  font-size: var(--vscode-font-size);
  font-weight: var(--vscode-font-weight);
  font-family: var(--vscode-font-family);
  background-color: var(--vscode-editor-background);
}

ol,
ul {
  padding-left: var(--container-padding);
}

body > *,
form > * {
  margin-block-start: var(--input-margin-vertical);
  margin-block-end: var(--input-margin-vertical);
}

*:focus {
  outline-color: var(--vscode-focusBorder) !important;
}

a {
  color: var(--vscode-textLink-foreground);
}

a:hover,
a:active {
  color: var(--vscode-textLink-activeForeground);
}

code {
  font-size: var(--vscode-editor-font-size);
  font-family: var(--vscode-editor-font-family);
  background-color: var(--vscode-textBlockQuote-background);
  padding: 2px 4px;
  border-radius: 3px;
}

button {
  border: none;
  padding: var(--input-padding-vertical) var(--input-padding-horizontal);
  width: auto;
  text-align: center;
  color: var(--vscode-button-foreground);
  background: var(--vscode-button-background);
  cursor: pointer;
  margin-right: 8px;
  margin-bottom: 8px;
}

button:hover {
  background: var(--vscode-button-hoverBackground);
}

button:focus {
  outline: 1px solid var(--vscode-focusBorder);
  outline-offset: 2px;
}

button.secondary {
  color: var(--vscode-button-secondaryForeground);
  background: var(--vscode-button-secondaryBackground);
}

button.secondary:hover {
  background: var(--vscode-button-secondaryHoverBackground);
}

input:not([type='checkbox']),
textarea {
  display: block;
  width: 100%;
  border: none;
  font-family: var(--vscode-font-family);
  padding: var(--input-padding-vertical) var(--input-padding-horizontal);
  color: var(--vscode-input-foreground);
  outline-color: var(--vscode-input-border);
  background-color: var(--vscode-input-background);
}

input::placeholder,
textarea::placeholder {
  color: var(--vscode-input-placeholderForeground);
}

.container {
  padding: var(--container-padding);
  max-width: 1200px;
  margin: 0 auto;
}

.hidden {
  display: none !important;
}

header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--vscode-panel-border);
  padding-bottom: 10px;
}

header h1 {
  margin: 0;
  padding: 0;
  font-size: 1.5em;
}

#session-status {
  margin-top: 5px;
  font-style: italic;
}

section {
  margin-bottom: 20px;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 3px;
}

.section-header {
  background-color: var(--vscode-panel-background);
  padding: 8px 12px;
  border-bottom: 1px solid var(--vscode-panel-border);
}

.section-header h2 {
  margin: 0;
  font-size: 1.2em;
}

.section-content {
  padding: 12px;
}

#prompt-input {
  min-height: 100px;
  margin-bottom: 10px;
  resize: vertical;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
}

#notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 15px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-width: 300px;
}

#notification.info {
  background-color: var(--vscode-notificationCenterHeader-background);
  color: var(--vscode-notificationCenterHeader-foreground);
}

#notification.warning {
  background-color: var(--vscode-editorWarning-foreground);
  color: var(--vscode-editor-background);
}

#notification.error {
  background-color: var(--vscode-editorError-foreground);
  color: var(--vscode-editor-background);
}

.step-item {
  padding: 8px;
  margin-bottom: 5px;
  border-radius: 3px;
  background-color: var(--vscode-editor-background);
  border-left: 3px solid transparent;
}

.step-item.active {
  border-left-color: var(--vscode-activityBarBadge-background);
  background-color: var(--vscode-list-activeSelectionBackground);
  color: var(--vscode-list-activeSelectionForeground);
}

.step-item.completed {
  border-left-color: var(--vscode-terminal-ansiGreen);
}

.step-item.skipped {
  border-left-color: var(--vscode-terminal-ansiYellow);
}

.finding-item {
  padding: 8px;
  margin-bottom: 10px;
  border-radius: 3px;
  background-color: var(--vscode-textBlockQuote-background);
}

.finding-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-weight: bold;
}

.finding-content {
  white-space: pre-wrap;
  overflow-x: auto;
}

.finding-content code {
  display: block;
  padding: 8px;
  margin: 5px 0;
  white-space: pre;
  overflow-x: auto;
}

.badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.8em;
  font-weight: normal;
}

.badge.code {
  background-color: var(--vscode-terminal-ansiBlue);
  color: var(--vscode-editor-background);
}

.badge.analysis {
  background-color: var(--vscode-terminal-ansiMagenta);
  color: var(--vscode-editor-background);
}

.badge.issue {
  background-color: var(--vscode-terminal-ansiRed);
  color: var(--vscode-editor-background);
}

.badge.solution {
  background-color: var(--vscode-terminal-ansiGreen);
  color: var(--vscode-editor-background);
}

/* Document Editor Styles */
.section-actions {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.action-button {
  margin-left: 8px;
  padding: 4px 8px;
  font-size: 0.9em;
}

.editor-section {
  margin-bottom: 20px;
}

.editor-section h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 1.1em;
}

#problem-statement-editor {
  width: 100%;
  min-height: 100px;
  resize: vertical;
  margin-bottom: 10px;
}

.step-editor-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 3px;
}

.step-editor-item input {
  flex-grow: 1;
  margin-right: 8px;
}

.step-editor-item select {
  width: 120px;
  margin-right: 8px;
  background-color: var(--vscode-dropdown-background);
  color: var(--vscode-dropdown-foreground);
  border: 1px solid var(--vscode-dropdown-border);
  padding: 4px;
}

.step-editor-actions {
  display: flex;
  align-items: center;
}

.step-editor-actions button {
  margin-left: 4px;
  padding: 2px 6px;
  font-size: 0.8em;
}

.step-move-up, .step-move-down {
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
}

.step-delete {
  background-color: var(--vscode-errorForeground);
  color: var(--vscode-editor-background);
}

.iteration-editor-item {
  margin-bottom: 12px;
  padding: 8px;
  background-color: var(--vscode-textBlockQuote-background);
  border-radius: 3px;
}

.iteration-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-weight: bold;
}

.iteration-findings {
  margin-top: 8px;
}

.finding-editor-item {
  margin-bottom: 8px;
  padding: 8px;
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 3px;
}

.finding-editor-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.finding-editor-content textarea {
  width: 100%;
  min-height: 60px;
  resize: vertical;
}
