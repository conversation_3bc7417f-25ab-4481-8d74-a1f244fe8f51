<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3498db;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2980b9;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect x="14" y="14" width="100" height="100" rx="10" ry="10" fill="url(#grad1)" />
  
  <!-- Circular arrow representing iteration -->
  <path d="M64,30 A30,30 0 1,1 34,60 A30,30 0 1,1 94,60" 
        stroke="white" stroke-width="6" fill="none" stroke-linecap="round" />
  
  <!-- Arrow head -->
  <polygon points="94,60 84,50 84,70" fill="white" />
  
  <!-- Steps/plan representation -->
  <rect x="44" y="80" width="40" height="6" rx="2" ry="2" fill="white" />
  <rect x="44" y="92" width="40" height="6" rx="2" ry="2" fill="white" />
  <rect x="44" y="104" width="40" height="6" rx="2" ry="2" fill="white" />
  
  <!-- Lightbulb representing insights/findings -->
  <path d="M64,45 Q64,35 70,35 Q76,35 76,45 Q76,55 70,55 Q64,55 64,45 Z" fill="white" />
  <rect x="66" y="55" width="8" height="4" rx="1" ry="1" fill="white" />
</svg>
