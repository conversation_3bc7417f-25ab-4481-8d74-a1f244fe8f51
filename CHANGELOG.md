# Change Log

All notable changes to the "Iterative Problem-Solving Assistant" extension will be documented in this file.

## [1.0.0] - 2023-11-15

### Added
- Complete Document Management System
  - Plan document creation and parsing
  - Organized folder structure (.ipsa/plans/active and .ipsa/plans/archived)
  - Document archiving and migration tools
  - Template support

- Complete Session Management System
  - Session creation, resumption, and switching
  - Session state persistence
  - User preferences management
  - Configuration settings
  - Status bar integration

- Complete Iteration Control System
  - Step progression (advance, go back, skip)
  - Iteration management
  - Step status tracking
  - Automatic step advancement
  - Session metrics

- Complete Agent Interaction System
  - Multiple assistant support (Clipboard, GitHub Copilot, Cursor AI)
  - Adapter pattern for different AI assistants
  - Prompt construction
  - Response capture
  - Assistant configuration

- Complete Output Management System
  - Code snippet extraction and integration
  - Documentation generation
  - Export capabilities
  - Multiple format support (Markdown, HTML, JSON)

### Changed
- Improved user interface and experience
- Enhanced error handling and logging
- Updated documentation

## [0.2.0] - 2023-10-15

### Added
- Iteration Control System
  - Step progression
  - Iteration management
  - Session metrics

- Agent Interaction System (partial)
  - Basic AI assistant integration
  - Prompt construction
  - Response capture

### Changed
- Improved document management
- Enhanced session management
- Updated user interface

## [0.1.0] - 2023-09-15

### Added
- Basic extension activation and command registration
- Document management system (creating, loading, and parsing plan documents)
- Session management (creating, resuming, ending, and switching between sessions)
- Status bar integration (showing current session status)
- Configuration settings and user preferences management
- State management system (persistent storage of session state)
- Findings extraction system (extracting and categorizing findings from AI responses)
- Prompt construction engine (generating contextual prompts with templates)

### Changed
- Initial release
