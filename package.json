{"name": "ipsa", "displayName": "Iterative Problem-Solving Assistant", "description": "A VS Code extension for structured, iterative problem-solving with AI assistants", "version": "1.0.0", "publisher": "ipsa-team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ipsa-team/ipsa"}, "icon": "images/ipsa-icon.png", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished", "workspaceContains:*.plan.md"], "main": "./dist/extension.js", "contributes": {"configuration": {"title": "IPSA", "properties": {"ipsa.preferredAssistant": {"type": "string", "enum": ["clipboard", "copilot", "cursor", "other"], "default": "clipboard", "description": "The preferred AI assistant type to use"}, "ipsa.git.autoCommit": {"type": "boolean", "default": false, "description": "Automatically commit plan document changes"}, "ipsa.prompt.maxPreviousFindings": {"type": "number", "default": 5, "description": "Maximum number of previous findings to include in prompts"}, "ipsa.plans.folderStructure": {"type": "string", "enum": ["flat", "organized"], "default": "organized", "description": "How plan documents are organized: 'flat' (all in workspace root) or 'organized' (in .ipsa/plans folders)"}, "ipsa.output.defaultDocumentationFormat": {"type": "string", "enum": ["markdown", "html", "json"], "default": "markdown", "description": "Default format for generated documentation"}, "ipsa.output.includeCodeSnippets": {"type": "boolean", "default": true, "description": "Whether to include code snippets in generated documentation by default"}, "ipsa.output.includeFindings": {"type": "boolean", "default": true, "description": "Whether to include findings in generated documentation by default"}, "ipsa.output.includeMetrics": {"type": "boolean", "default": true, "description": "Whether to include metrics in generated documentation by default"}, "ipsa.output.defaultExportFormat": {"type": "string", "enum": ["markdown", "html", "json"], "default": "markdown", "description": "Default format for exporting plan documents"}}}, "commands": [{"command": "ipsa.startNewSession", "title": "IPSA: Start New Iterative Session"}, {"command": "ipsa.resumeSession", "title": "IPSA: Resume Iterative Session"}, {"command": "ipsa.endSession", "title": "IPSA: End Current Session"}, {"command": "ipsa.createPlanDocument", "title": "IPSA: Create Plan Document"}, {"command": "ipsa.openPlanDocument", "title": "IPSA: Open Plan Document"}, {"command": "ipsa.showSessionStatus", "title": "IPSA: Show Session Status"}, {"command": "ipsa.listSessions", "title": "IPSA: List All Sessions"}, {"command": "ipsa.managePreferences", "title": "IPSA: Manage Preferences"}, {"command": "ipsa.constructPrompt", "title": "IPSA: Construct Prompt"}, {"command": "ipsa.extractFindings", "title": "IPSA: Extract Findings from Response"}, {"command": "ipsa.createFinding", "title": "IPSA: Create Finding from Selection"}, {"command": "ipsa.migratePlanDocuments", "title": "IPSA: Migrate Plan Documents to Organized Folder"}, {"command": "ipsa.migrateSessionPaths", "title": "IPSA: Update Session Paths to Organized Folder"}, {"command": "ipsa.archivePlanDocument", "title": "IPSA: Archive Plan Document"}, {"command": "ipsa.startNewIteration", "title": "IPSA: Start New Iteration"}, {"command": "ipsa.advanceToNextStep", "title": "IPSA: Advance to Next Step"}, {"command": "ipsa.goToPreviousStep", "title": "IPSA: Go to Previous Step"}, {"command": "ipsa.skipCurrentStep", "title": "IPSA: <PERSON>p Current Step"}, {"command": "ipsa.markStepAsCompleted", "title": "IPSA: <PERSON> Step as Completed"}, {"command": "ipsa.showSessionMetrics", "title": "IPSA: Show Session Metrics"}, {"command": "ipsa.configureAssistant", "title": "IPSA: Configure AI Assistant"}, {"command": "ipsa.sendPromptToAssistant", "title": "IPSA: Send Prompt to AI Assistant"}, {"command": "ipsa.captureAssistantResponse", "title": "IPSA: Capture AI Assistant Response"}, {"command": "ipsa.interactWithAssistant", "title": "IPSA: Interact with AI Assistant"}, {"command": "ipsa.extractCodeSnippets", "title": "IPSA: Extract Code Snippets"}, {"command": "ipsa.applyCodeSnippet", "title": "IPSA: Apply Code Snippet"}, {"command": "ipsa.generateDocumentation", "title": "IPSA: Generate Documentation"}, {"command": "ipsa.exportPlanDocument", "title": "IPSA: Export Plan Document"}, {"command": "ipsa.showIPSAPanel", "title": "IPSA: Show Panel"}, {"command": "ipsa.openDocumentEditor", "title": "IPSA: Open Document Editor"}, {"command": "ipsa.updateProblemStatement", "title": "IPSA: Update Problem Statement"}, {"command": "ipsa.addPlanStep", "title": "IPSA: Add Plan Step"}, {"command": "ipsa.updatePlanStep", "title": "IPSA: Update Plan Step"}, {"command": "ipsa.removePlanStep", "title": "IPSA: Remove Plan Step"}, {"command": "ipsa.reorderPlanSteps", "title": "IPSA: Reorder Plan Steps"}, {"command": "ipsa.saveDocumentChanges", "title": "IPSA: Save Document Changes"}]}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "compile:dev": "webpack --mode development", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "compile-tests": "tsc -p ./", "watch-tests": "tsc -p ./ --watch", "pretest": "npm run compile-tests && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "start": "node taskmaster-client.js", "generate": "node taskmaster-client.js generate", "vsce:package": "vsce package"}, "devDependencies": {"@types/glob": "^7.2.0", "@types/mocha": "^9.1.1", "@types/node": "16.x", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^5.30.0", "@typescript-eslint/parser": "^5.30.0", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "^2.22.0", "canvas": "^3.1.0", "copy-webpack-plugin": "^13.0.0", "eslint": "^8.18.0", "glob": "^8.0.3", "mocha": "^10.0.0", "ts-loader": "^9.3.1", "typescript": "^4.7.4", "webpack": "^5.73.0", "webpack-cli": "^4.10.0"}, "dependencies": {"@types/markdown-it": "^14.1.2", "chalk": "^4.1.2", "commander": "^9.4.1", "markdown-it": "^13.0.2"}}